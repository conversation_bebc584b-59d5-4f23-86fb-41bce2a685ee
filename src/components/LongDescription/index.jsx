    import React from 'react';
    import styles from './style.module.scss'; // Utilisez ce fichier SCSS pour les styles
    import GSAPTextReveal from '@/components/GSAPTextReveal';
    import { getPreset } from '@/components/GSAPTextReveal/presets';


export default function LongDescription({

    // const LongDescription = ({
    title = "Un projet entre modernité technologique et direction artistique nostalgique",
    leftText = "YUL représente tout ce que l’on aime de Montréal: un parfait mélange des styles dans un territoire créatif étonnant. À travers les yeux de la chouette, animal emblématique du Québec, on découvre les quartiers qui composent la métropole sous la forme de chapitres à explorer. Bâtie sur l’environnement WebGL, l’univers numérique est doté d’un mixte de 3D et d'illustrations faites sur mesure par nos artisans et inspirées par la trame urbaine montréalaise.",
    rightText = "Une attention particulière a été portée sur la signature typographique. La combinaison des polices DaVinci et TAN Waverly vise à porter l’expérience de narration à une sensation de livre illustré alors que la technologie WebGL lui confère une dimension de bande dessinée animée. Le meilleur des mondes, dans les deux langues propres à l’agence (créativité et technologie) et celles de notre métropole bilingue."
    // }) => {
    }) {
        return (
            <div className="container section">
                <div className={styles.longDescription}>
            
                    <GSAPTextReveal
                        as="h2"
                        className={styles.title}
                        {...getPreset('hero')}
                    >
                        {title}
                    </GSAPTextReveal>
                    
                        <div className={styles.textContainer}>
                            <div className={styles.leftColumn}>
                                <GSAPTextReveal
                                    as="p"
                                    className={`${styles.text} text-big`}
                                    {...getPreset('lines', { delay: 0.8, stagger: 0.2 })}
                                >
                                    {leftText}
                                </GSAPTextReveal>
                            </div>
                            <div className={styles.rightColumn}>
                                <GSAPTextReveal
                                    as="p"
                                    className={`${styles.text} text-big`}
                                    {...getPreset('lines', { delay: 1.1, stagger: 0.2 })}
                                >
                                    {rightText}
                                </GSAPTextReveal>
                            </div>
                        </div>
                </div>
            </div>
    );
    };

    // export default LongDescription;
