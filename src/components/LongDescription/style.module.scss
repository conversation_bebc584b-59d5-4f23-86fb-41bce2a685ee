.longDescription {
  .textContainer {
    display: flex;
    flex-direction: column;
  }
  }
  
  @media (min-width:1200px) {
    .title {
        max-width: 50%;
        margin-bottom: var(--gap-padding);
    }

    .textContainer {
        flex-flow: row wrap;
        column-gap: calc(var(--section-padding) / 4);
        justify-content: flex-end;
        width: 100%;
        flex-direction: row !important;
    
        .leftColumn,
        .rightColumn {
          width: calc(33.3333% - 10px);
          max-width: 327px;
    
          .text {
            display: block;
            margin: 0;
            padding: 0;
          }
        }
      }
  }